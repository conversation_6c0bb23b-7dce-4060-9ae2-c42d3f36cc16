<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/exercise/' . $exercise['id'] . '/applicants') ?>">Profiling Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Profiling Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('profile_applications_exercise/exercise/' . $exercise['id'] . '/applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Applicant Profile -->
<div class="row">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Applicant Profile: <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active text-dark" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fas fa-user-circle"></i> Applicant Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link text-dark" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false">
                            <i class="fas fa-file-pdf"></i> Files (<?= count($applicant_files) ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link text-dark" id="applications-tab" data-bs-toggle="tab" data-bs-target="#applications" type="button" role="tab" aria-controls="applications" aria-selected="false">
                            <i class="fas fa-briefcase"></i> Applications (<?= count($applications) ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="profileTabsContent">
                    <!-- Applicant Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <?php $firstApp = $applications[0]; ?>

                                <!-- 1. Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-user me-2"></i>Personal Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Basic details and contact info</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Full Name:</strong> <?= esc($firstApp['first_name'] . ' ' . $firstApp['last_name']) ?></p>
                                                <p><strong>Gender:</strong> <?= esc($firstApp['gender']) ?></p>
                                                <p><strong>Date of Birth:</strong> <?= esc($firstApp['date_of_birth']) ?></p>
                                                <p><strong>Place of Origin:</strong> <?= esc($firstApp['place_of_origin']) ?></p>
                                                <p><strong>Citizenship:</strong> <?= esc($firstApp['citizenship']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Email:</strong> <?= esc($firstApp['email_address']) ?></p>
                                                <p><strong>Contact Details:</strong> <?= esc($firstApp['contact_details']) ?></p>
                                                <p><strong>Address:</strong> <?= esc($firstApp['location_address']) ?></p>
                                                <p><strong>Marital Status:</strong> <?= esc($firstApp['marital_status']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 2. Documents & ID -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-id-card me-2"></i>Documents & ID
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Identification and records</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>ID Numbers:</strong> <?= esc($firstApp['id_numbers']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Passport Number:</strong> <?= esc($firstApp['passport_number']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 3. Pre-screening Status -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-check-circle me-2"></i>Pre-screening Status
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Pre-screening results and status</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Pre-screening Status:</strong> 
                                                    <span class="badge bg-success">Passed</span>
                                                </p>
                                                <p><strong>Pre-screened Date:</strong> <?= esc($firstApp['pre_screened_at']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Pre-screening Remarks:</strong> <?= esc($firstApp['pre_screened_remarks']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No application details found for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Files Tab -->
                    <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                        <div class="p-4">
                            <?php if (!empty($applicant_files)): ?>
                                <div class="row">
                                    <?php foreach ($applicant_files as $file): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card border">
                                                <div class="card-body">
                                                    <h6 class="card-title">
                                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                                        <?= esc($file['file_title']) ?>
                                                    </h6>
                                                    <p class="card-text small text-muted">
                                                        <?= esc($file['file_description'] ?? 'Application file') ?>
                                                    </p>
                                                    <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View File
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No files uploaded for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Applications Tab -->
                    <div class="tab-pane fade" id="applications" role="tabpanel" aria-labelledby="applications-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Application #</th>
                                                <th>Position</th>
                                                <th>Group</th>
                                                <th>Location</th>
                                                <th>Applied Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($applications as $app): ?>
                                                <tr>
                                                    <td><?= esc($app['application_number']) ?></td>
                                                    <td>
                                                        <strong><?= esc($app['position_title']) ?></strong><br>
                                                        <small class="text-muted"><?= esc($app['position_reference']) ?></small>
                                                    </td>
                                                    <td><?= esc($app['group_name']) ?></td>
                                                    <td><?= esc($app['position_location']) ?></td>
                                                    <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                                    <td>
                                                        <span class="badge bg-success">Pre-screened</span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No applications found for this applicant in this exercise.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Calculate age if date of birth is available
    const dobElement = document.getElementById('dobDisplay');
    const ageElement = document.getElementById('ageDisplay');
    
    if (dobElement && ageElement) {
        const dob = dobElement.textContent.trim();
        if (dob && dob !== '') {
            const birthDate = new Date(dob);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            
            if (age >= 0 && age <= 120) {
                ageElement.textContent = age + ' years old';
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
